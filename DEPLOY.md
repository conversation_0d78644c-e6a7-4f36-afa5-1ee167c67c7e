# Note de Déploiement - HAI Systems Landing Page

## 1. Déploiement sur Vercel (ou Netlify)
- Pousser le repository sur GitHub : `git push origin main`
- Créer un nouveau projet sur Vercel ou Netlify en liant le repository GitHub
- Sélectionner le framework (Vite/React) et confirmer la configuration par défaut

## 2. Variables d'environnement
Dans le dashboard du projet, ajouter les variables suivantes :
- `AIRTABLE_KEY` : [insérer la valeur de votre clé API Airtable]
- `AIRTABLE_BASE` : [insérer l'ID de votre base Airtable]

## 3. (Optionnel) Configuration GA4
Remplacer `G-XXXXXXX` par votre ID GA4 réel dans le fichier `index.html` (ligne contenant le script Google Analytics).

## 4. Commande de déploiement
- Pour déployer : `git push origin main`
- Vercel/Netlify déclenchera automatiquement le build et le déploiement

## 5. Test rapide post-déploiement
- Accéder au site déployé
- Soumettre le formulaire de contact
- Vérifier qu'une nouvelle ligne apparaît dans votre base Airtable
- Vérifier que les événements sont trackés dans Google Analytics 4
